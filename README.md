# YGameBuildTool - MSBuild 快速编译工具

![版本](https://img.shields.io/badge/版本-v1.8.0-blue.svg)
![平台](https://img.shields.io/badge/平台-Windows-lightgrey.svg)
![语言](https://img.shields.io/badge/语言-Python-green.svg)

一个专为游戏开发团队设计的 MSBuild 图形化编译工具，支持多配置管理、实时输出显示和自定义编译命令。

## ✨ 主要功能

### 🎯 核心特性
- **多配置管理** - 支持创建、切换、重命名和删除多个编译配置
- **项目类型支持** - 支持 Editor、Server、Client 三种项目类型的独立或组合编译
- **实时输出显示** - 编译过程实时显示，支持流式输出和错误高亮
- **自定义编译命令** - 完全可定制的 MSBuild 参数和选项
- **进程管理** - 智能进程管理，确保同时只运行一个编译任务

### 🛠️ 编译功能
- **Build/Rebuild 模式** - 支持增量编译和完全重建
- **Debug/Release 配置** - 快速切换调试和发布模式
- **多项目并行编译** - 自动检测项目依赖，优化编译顺序
- **Make.bat 执行** - 支持执行项目根目录和服务器目录的 make.bat 脚本

### 🎨 界面特性
- **标签页管理** - 每个配置独立的标签页，支持拖拽调整
- **响应式布局** - 自适应窗口大小，支持高DPI显示
- **右键菜单** - 便捷的配置管理操作
- **状态指示** - 清晰的编译状态和进度显示

## 🚀 快速开始

### 系统要求
- Windows 10/11
- Python 3.7+ (如果从源码运行)
- Visual Studio 2019/2022 (包含 MSBuild)
- .NET Framework 4.7.2+

### 安装方式

#### 方式一：直接运行 (推荐)
1. 下载最新版本的 `YGameBuildTool_v1.8.0.exe`
2. 双击运行即可，无需安装

#### 方式二：从源码运行
```bash
# 克隆仓库
git clone <repository-url>
cd msbuildtool

# 安装依赖
pip install -r requirements.txt

# 运行程序
python msbuild.py
```

## 📖 使用指南

### 首次配置

1. **设置项目根目录**
   - 点击"浏览"按钮选择你的游戏项目根目录
   - 确保目录包含相应的项目文件

2. **选择项目类型**
   - ☑️ **Editor** - 编辑器相关项目
   - ☑️ **Server** - 服务器项目  
   - ☑️ **Client** - 客户端项目

3. **配置 MSBuild 路径**
   - 工具会自动检测 Visual Studio 安装路径
   - 如需手动设置，点击"浏览"选择 MSBuild.exe

4. **自定义编译命令**
   - 在右侧文本框中添加自定义的 MSBuild 参数
   - 默认包含常用的优化参数

### 基本操作

#### 编译项目
1. 选择编译模式：`Build` 或 `Rebuild`
2. 选择配置模式：`Debug` 或 `Release`
3. 勾选要编译的项目类型
4. 点击 **"编译"** 按钮开始编译

#### 管理配置
- **新增配置**：右键标签页 → "新增配置"
- **重命名配置**：双击标签页标题 或 右键 → "重命名此配置"
- **删除配置**：右键标签页 → "删除此配置"
- **切换配置**：直接点击标签页

#### 执行脚本
- **根目录 make.bat**：点击 "执行make.bat" 按钮
- **服务器 make.bat**：点击 "执行server/make.bat" 按钮

### 高级功能

#### 自定义编译参数
在自定义命令文本框中，你可以添加各种 MSBuild 参数：

```bash
# 示例参数
/nr:false                    # 禁用节点重用
/p:Platform=x64             # 指定平台
/verbosity:detailed         # 详细输出
/m                          # 多核编译
/fl                         # 输出到文件
/p:AdditionalOptions="/Zm500"  # 增加编译器内存
```

#### 日志管理
- 点击 **"日志位置"** 按钮打开日志文件夹
- 编译日志自动保存到临时目录
- 支持详细日志和错误日志分离

## 🎯 支持的项目结构

工具支持以下项目结构：

```
项目根目录/
├── .build-editor/
│   ├── Engine/KApplication/KGameEditor.vcxproj
│   └── Editor/ImNodeGraphEditor/Editor/ImNodeGraphEditor.vcxproj
├── Server/SceneServer/.build/SceneServer.sln
├── .build/ygame-code.sln
├── make.bat
└── Server/make.bat
```

## ⚙️ 配置文件

配置信息自动保存在 `msbuild_config.json` 文件中：

```json
{
    "name": "我的配置",
    "is_rebuild": false,
    "is_release": true,
    "selected_project_types": ["editor", "client"],
    "root_directory": "D:/MyGame",
    "msbuild_path": "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/MSBuild.exe",
    "custom_commands": "/nr:false\n/p:Platform=x64\n/verbosity:detailed"
}
```

## 🔧 故障排除

### 常见问题

**Q: 提示"MSBuild.exe 文件路径无效"**
A: 确保安装了 Visual Studio 2019/2022，或手动设置正确的 MSBuild.exe 路径

**Q: 编译时提示"项目文件不存在"**
A: 检查项目根目录设置是否正确，确保包含相应的项目文件

**Q: 编译过程中断**
A: 点击"中断"按钮可以安全停止编译过程

**Q: 界面显示模糊**
A: 工具已启用高DPI感知，如仍有问题请检查系统显示设置

### 日志分析
- 编译输出实时显示在下方终端区域
- 错误信息会以 `[ERROR]` 前缀标识
- 详细日志保存在系统临时目录

## 📝 更新日志

### v1.8.0 (2025-08-05)
- ✅ 支持多配置管理
- ✅ 支持自定义编译命令  
- ✅ 实时显示编译输出
- ✅ 自动保存配置
- ✅ 支持拖拽调整界面
- ✅ 修复右键菜单错误
- ✅ 优化进程管理，确保唯一进程

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**开发团队**: MSBuild Tool Team  
**技术支持**: 如有问题请提交 Issue
