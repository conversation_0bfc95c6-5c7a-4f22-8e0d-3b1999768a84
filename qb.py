#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuickBuild API 分析工具
用于分析编译失败到成功的时间消耗
"""

import requests
import xml.etree.ElementTree as ET
from datetime import datetime
import pandas as pd
from typing import List, Dict, Optional
import argparse
import sys


class BuildInfo:
    """构建信息类"""
    def __init__(self, build_id: str, version: str, status: str, status_date: str, begin_date: str, duration: int):
        self.build_id = build_id
        self.version = version
        self.status = status
        self.status_date = datetime.fromisoformat(status_date.replace('Z', '+00:00'))
        self.begin_date = datetime.fromisoformat(begin_date.replace('Z', '+00:00'))
        self.duration = duration  # 毫秒


class QuickBuildAnalyzer:
    """QuickBuild 分析器"""

    def __init__(self, base_url: str = "https://qb.ygame.work"):
        self.base_url = base_url

    def fetch_builds(self, configuration_id: int, from_date: str, count: int = 100000) -> str:
        """获取构建数据"""
        url = f"{self.base_url}/rest/builds"
        params = {
            'configuration_id': configuration_id,
            'from_date': from_date,
            'count': count
        }

        try:
            print(f"正在获取构建数据: {url}")
            print(f"参数: {params}")

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            print(f"成功获取数据，响应大小: {len(response.text)} 字符")
            return response.text

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            sys.exit(1)

    def parse_xml(self, xml_content: str) -> List[BuildInfo]:
        """解析XML内容"""
        try:
            root = ET.fromstring(xml_content)
            builds = []

            # 查找所有的 Build 元素
            for build_elem in root.findall('.//com.pmease.quickbuild.model.Build'):
                try:
                    build_id = build_elem.find('id').text
                    version = build_elem.find('version').text
                    status = build_elem.find('status').text
                    status_date = build_elem.find('statusDate').text
                    begin_date = build_elem.find('beginDate').text
                    duration = int(build_elem.find('duration').text)

                    build_info = BuildInfo(build_id, version, status, status_date, begin_date, duration)
                    builds.append(build_info)

                except (AttributeError, ValueError) as e:
                    print(f"解析构建信息时出错: {e}")
                    continue

            print(f"成功解析 {len(builds)} 个构建记录")
            return builds

        except ET.ParseError as e:
            print(f"XML解析失败: {e}")
            sys.exit(1)

    def analyze_failure_to_success(self, builds: List[BuildInfo]) -> List[Dict]:
        """分析失败到成功的时间消耗"""
        # 按时间排序（最早的在前）
        builds.sort(key=lambda x: x.status_date)

        results = []
        i = 0

        while i < len(builds):
            current_build = builds[i]

            # 如果当前构建失败，寻找下一个成功的构建
            if current_build.status == 'FAILED':
                first_failed_build = current_build

                # 寻找后续的成功构建
                j = i + 1
                while j < len(builds):
                    next_build = builds[j]
                    if next_build.status == 'SUCCESSFUL':
                        # 计算时间差
                        time_diff = next_build.status_date - first_failed_build.status_date
                        time_consumed_hours = time_diff.total_seconds() / 60

                        result = {
                            '失败版本号': first_failed_build.version,
                            '失败时间': first_failed_build.status_date.strftime('%Y-%m-%d %H:%M:%S'),
                            '成功版本号': next_build.version,
                            '成功时间': next_build.status_date.strftime('%Y-%m-%d %H:%M:%S'),
                            '消耗时间(小时)': round(time_consumed_hours, 2),
                            '消耗时间(天)': round(time_consumed_hours / 24, 2)
                        }
                        results.append(result)
                        # 跳过到成功构建的位置，避免重复计算
                        i = j
                        break
                    j += 1
                else:
                    # 如果没有找到成功的构建，跳出循环
                    break
            else:
                i += 1

        return results

    def generate_report(self, results: List[Dict], output_file: str = None):
        """生成报告"""
        if not results:
            print("没有找到失败到成功的构建记录")
            return

        # 创建DataFrame
        df = pd.DataFrame(results)

        # 显示统计信息
        print(f"\n=== 编译失败到成功分析报告 ===")
        print(f"总共找到 {len(results)} 个失败到成功的案例")
        print(f"平均消耗时间: {df['消耗时间(小时)'].mean():.2f} 小时")
        print(f"最长消耗时间: {df['消耗时间(小时)'].max():.2f} 小时")
        print(f"最短消耗时间: {df['消耗时间(小时)'].min():.2f} 小时")

        # 显示表格
        print(f"\n=== 详细记录 ===")
        print(df.to_string(index=False))

        # 保存到文件
        if output_file:
            if output_file.endswith('.xlsx'):
                df.to_excel(output_file, index=False)
                print(f"\n报告已保存到: {output_file}")
            elif output_file.endswith('.csv'):
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                print(f"\n报告已保存到: {output_file}")
            else:
                print(f"不支持的文件格式: {output_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='QuickBuild 编译失败到成功分析工具')
    parser.add_argument('--config-id', type=int, default=11, help='配置ID (默认: 11)')
    parser.add_argument('--from-date', default='2025-06-01', help='开始日期 (默认: 2025-07-01)')
    parser.add_argument('--count', type=int, default=100000, help='获取记录数量 (默认: 100000)')
    parser.add_argument('--output',default='res3.xlsx', help='输出文件路径 (.xlsx 或 .csv)')
    parser.add_argument('--base-url', default='https://qb.ygame.work', help='QuickBuild 基础URL')

    args = parser.parse_args()

    # 创建分析器
    analyzer = QuickBuildAnalyzer(args.base_url)

    # 获取数据
    xml_content = analyzer.fetch_builds(args.config_id, args.from_date, args.count)

    # 解析数据
    builds = analyzer.parse_xml(xml_content)

    # 分析失败到成功的情况
    results = analyzer.analyze_failure_to_success(builds)

    # 生成报告
    analyzer.generate_report(results, args.output)


if __name__ == '__main__':
    main()