import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import json
import os
import subprocess
import threading
import queue
import sys
import ctypes # Added for DPI awareness
import tempfile # Added for temp directory
import datetime # Added for date-based log naming
import argparse # Added for command line argument parsing
import shutil # Added for file copying

class MSBuildTool(tk.Tk):
    """
    一个用于快速操作 MSBuild 的图形化工具。
    它支持保存多个编译配置，并通过分页进行切换。
    """
    CONFIG_FILE = "msbuild_config.json"
    VERSION = "2.3.8"  # 主版本.次版本.修订号
    VERSION_INFO = {
        "version": VERSION,
        "date": "2025-08-08",
        "author": "MSBuild Tool",
        "changes": [
            "1. 支持多配置管理",
            "2. 支持自定义编译命令",
            "3. 实时显示编译输出",
            "4. 自动保存配置",
            "5. 支持拖拽调整界面"
        ]
    }

    def __init__(self):
        super().__init__()
        # --- 核心状态 ---
        self.configs = self.load_configs()
        self.build_process = None
        self.build_queue = queue.Queue()
        self.ui_widgets = [] # 用于统一管理所有配置页的控件

        # --- 窗口初始化 ---
        self.title(f"YGameBuildTool v{self.VERSION}")
        self.geometry("1200x800")  # 增加窗口初始大小
        
        # 设置窗口图标（如果有的话）
        try:
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                application_path = sys._MEIPASS
            else:
                # 如果是python脚本
                application_path = os.path.dirname(os.path.abspath(__file__))
            icon_path = os.path.join(application_path, "msbuild.ico")
            if os.path.exists(icon_path):
                self.iconbitmap(icon_path)
        except Exception:
            pass

        # 在Windows上提高DPI感知，防止界面模糊
        try:
            if sys.platform == "win32":
                ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except Exception:
            pass

        # --- UI构建 ---
        self.setup_ui()
        self.setup_notebook_menu()
        self.process_build_queue()

        # --- 事件绑定 ---
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        """构建主界面布局"""
        main_paned_window = ttk.PanedWindow(self, orient=tk.VERTICAL)
        main_paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- 上部面板: 配置区 ---
        top_frame = ttk.Frame(main_paned_window, padding="10")
        main_paned_window.add(top_frame, weight=1)

        self.notebook = ttk.Notebook(top_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        self.create_config_tabs()

        # --- 下部面板: 终端区 ---
        bottom_frame = ttk.Frame(main_paned_window)
        main_paned_window.add(bottom_frame, weight=2)
        bottom_frame.columnconfigure(0, weight=1)
        bottom_frame.rowconfigure(0, weight=1)
        
        # --- 命令终端 ---
        terminal_frame = ttk.Labelframe(bottom_frame, text="命令终端")
        terminal_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        terminal_frame.rowconfigure(0, weight=1)
        terminal_frame.columnconfigure(0, weight=1)

        self.terminal = scrolledtext.ScrolledText(
            terminal_frame,
            wrap=tk.WORD,
            state='disabled',
            bg="black",                # 背景黑色
            fg="lightgreen",           # 字体绿色
            insertbackground="white"   # 光标白色
        )
        self.terminal.pack(fill=tk.BOTH, expand=True)

        # 显示当前选中标签页的项目信息
        self.show_current_tab_project_info()

    def create_config_tabs(self):
        """根据配置创建所有的Tab页"""
        for i, config in enumerate(self.configs):
            tab = ttk.Frame(self.notebook, padding="10")
            self.notebook.add(tab, text=config.get("name", f"配置 {i+1}"))
            tab_widgets = self.create_tab_content(tab, config)
            self.ui_widgets.append(tab_widgets)

    def open_log_file(self):
        """打开日志文件所在文件夹"""
        try:
            # 获取当前选中标签页的项目类型
            current_tab_index = self.notebook.index(self.notebook.select())
            current_widgets = self.ui_widgets[current_tab_index]
            selected_types = [ptype for ptype, var in current_widgets['project_type_vars'].items() if var.get()]

            # 生成当前项目的日志文件名
            build_log, error_log = self.generate_log_filenames(selected_types)
            log_dir = os.path.dirname(build_log)

            # 在Windows上打开文件夹并选中日志文件
            if sys.platform == "win32":
                # 优先选中构建日志文件，如果不存在则选中错误日志文件
                if os.path.exists(build_log):
                    subprocess.run(['explorer', '/select,', build_log])
                elif os.path.exists(error_log):
                    subprocess.run(['explorer', '/select,', error_log])
                else:
                    # 如果日志文件都不存在，只打开日志目录
                    subprocess.run(['explorer', log_dir])
            else:
                # 非Windows系统直接打开文件夹
                os.startfile(log_dir)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志文件夹: {str(e)}")

    def create_tab_content(self, parent_tab, config):
        """为一个Tab页创建所有UI控件"""
        parent_tab.columnconfigure(1, weight=1)
        parent_tab.rowconfigure(0, weight=1)

        widgets = {}

        # --- 左右布局 ---
        left_frame = ttk.Frame(parent_tab)
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)  # 让左侧框架可以水平扩展
        left_frame.rowconfigure(4, weight=1)  # 让空白区域可以扩展，将按钮推到底部

        right_frame = ttk.Frame(parent_tab)
        right_frame.grid(row=0, column=1, sticky="nsew")
        right_frame.rowconfigure(1, weight=1)
        right_frame.columnconfigure(0, weight=1)

        parent_tab.columnconfigure(0, weight=2)  # 左侧占比
        parent_tab.columnconfigure(1, weight=3)  # 右侧占比更大，给自定义命令更多空间

        # --- 左侧选项 ---
        options_frame = ttk.Frame(left_frame)
        options_frame.grid(row=0, column=0, sticky="ew")
        options_frame.columnconfigure(1, weight=1)

        # Build/Rebuild 模式 (使用Combobox替换Checkbutton)
        ttk.Label(options_frame, text="编译模式:").grid(row=0, column=0, sticky="w", pady=5)
        build_mode_var = tk.StringVar(value="Rebuild" if config.get("is_rebuild", False) else "Build")
        build_combo = ttk.Combobox(options_frame, textvariable=build_mode_var, values=["Build", "Rebuild"], state="readonly")
        build_combo.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        # Debug/Release 模式 (使用Combobox替换Checkbutton)
        ttk.Label(options_frame, text="配置模式:").grid(row=1, column=0, sticky="w", pady=5)
        config_mode_var = tk.StringVar(value="Release" if config.get("is_release", False) else "Debug")
        config_combo = ttk.Combobox(options_frame, textvariable=config_mode_var, values=["Debug", "Release"], state="readonly")
        config_combo.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # 项目类型选择（多选框）
        ttk.Label(options_frame, text="项目类型:").grid(row=2, column=0, sticky="w", pady=5)

        # 创建多选框框架
        project_type_frame = ttk.Frame(options_frame)
        project_type_frame.grid(row=2, column=1, sticky="w", padx=5, pady=5)

        # 创建多选框变量
        project_types = ["editor", "server", "client"]
        project_type_vars = {}

        # 从配置中获取已选择的项目类型（支持向后兼容）
        selected_types = config.get("selected_project_types", [])
        if not selected_types and config.get("project_type"):
            # 向后兼容：如果没有新的多选配置，使用旧的单选配置
            selected_types = [config.get("project_type")]

        for i, ptype in enumerate(project_types):
            var = tk.BooleanVar(value=ptype in selected_types)
            project_type_vars[ptype] = var
            checkbox = ttk.Checkbutton(project_type_frame, text=ptype, variable=var,
                                     command=lambda: self.on_project_type_change(project_type_vars))
            checkbox.grid(row=0, column=i, sticky="w", padx=(0, 10))

        # 绑定项目类型变化事件，用于显示项目信息
        def on_project_type_change_wrapper():
            selected = [ptype for ptype, var in project_type_vars.items() if var.get()]
            if selected and hasattr(self, 'terminal'):
                self.update_project_info_display(selected)

        # 项目根目录地址
        root_dir_frame = ttk.Frame(left_frame)
        root_dir_frame.grid(row=1, column=0, sticky="ew", pady=(10, 5))
        root_dir_frame.columnconfigure(1, weight=1)  # 让输入框可以水平扩展

        ttk.Label(root_dir_frame, text="项目根目录:").grid(row=0, column=0, sticky="w")
        root_dir_var = tk.StringVar(value=config.get("root_directory", ""))
        root_dir_entry = ttk.Entry(root_dir_frame, textvariable=root_dir_var)
        root_dir_entry.grid(row=0, column=1, sticky="ew", padx=5)
        root_dir_browse_button = ttk.Button(root_dir_frame, text="...", width=3,
                                          command=lambda: self.browse_directory(root_dir_var))
        root_dir_browse_button.grid(row=0, column=2, sticky="e")

        # MSBuild 地址
        msbuild_frame = ttk.Frame(left_frame)
        msbuild_frame.grid(row=2, column=0, sticky="ew", pady=(5, 10))
        msbuild_frame.columnconfigure(1, weight=1)  # 让输入框可以水平扩展

        ttk.Label(msbuild_frame, text="MSBuild地址:").grid(row=0, column=0, sticky="w")
        msbuild_path_var = tk.StringVar(value=config.get("msbuild_path", ""))
        msbuild_entry = ttk.Entry(msbuild_frame, textvariable=msbuild_path_var)
        msbuild_entry.grid(row=0, column=1, sticky="ew", padx=5)
        msbuild_browse_button = ttk.Button(msbuild_frame, text="...", width=3,
                                         command=lambda: self.browse_exe(msbuild_path_var, "MSBuild.exe"))
        msbuild_browse_button.grid(row=0, column=2, sticky="e")

        # --- 编译按钮和日志按钮 (放在左下角) ---
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=5, column=0, sticky="sw", padx=5, pady=5)

        # 为每个标签页创建按钮
        compile_button = ttk.Button(button_frame, text="编译", command=self.start_build_thread)
        compile_button.pack(side=tk.LEFT, padx=(0, 5))

        # 添加中断按钮
        stop_button = ttk.Button(button_frame, text="中断", command=self.stop_build_process, state='disabled')
        stop_button.pack(side=tk.LEFT, padx=(0, 5))

        # 添加根目录make.bat按钮
        root_make_button = ttk.Button(button_frame, text="执行make.bat", command=self.start_root_make_thread)
        root_make_button.pack(side=tk.LEFT, padx=(0, 5))

        # 添加服务器make.bat按钮
        server_make_button = ttk.Button(button_frame, text="执行server/make.bat", command=self.start_server_make_thread)
        server_make_button.pack(side=tk.LEFT, padx=(0, 5))

        # 添加打开日志文件夹按钮
        log_button = ttk.Button(button_frame, text="日志位置", command=self.open_log_file)
        log_button.pack(side=tk.LEFT)

        # --- 右侧自定义命令 (增大尺寸) ---
        ttk.Label(right_frame, text="自定义命令:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        custom_cmd_text = tk.Text(right_frame, height=15, wrap=tk.WORD)  # 大幅增加文本框高度
        custom_cmd_text.grid(row=1, column=0, sticky="nsew")
        custom_cmd_text.insert("1.0", config.get("custom_commands", ""))

        # 保存控件引用以便之后读取和禁用
        checkboxes = [project_type_frame.winfo_children()[i] for i in range(len(project_types))]
        widgets.update({
            'build_mode_var': build_mode_var,
            'config_mode_var': config_mode_var,
            'project_type_vars': project_type_vars,
            'root_dir_var': root_dir_var,
            'msbuild_path_var': msbuild_path_var,
            'custom_cmd_text': custom_cmd_text,
            'compile_button': compile_button,
            'stop_button': stop_button,
            'root_make_button': root_make_button,
            'server_make_button': server_make_button,
            'log_button': log_button,
            'controls': [build_combo, config_combo] + checkboxes + [root_dir_entry, root_dir_browse_button, msbuild_entry, msbuild_browse_button, custom_cmd_text]
        })
        return widgets
    
    def update_label_text(self, label_var, check_var, off_text, on_text):
        """根据复选框状态更新标签文本"""
        label_var.set(on_text if check_var.get() else off_text)

    def browse_directory(self, dir_path_var):
        """打开文件对话框选择目录"""
        directory = filedialog.askdirectory(
            title="选择项目根目录"
        )
        if directory:
            dir_path_var.set(directory)

    def browse_exe(self, path_var, title):
        """打开文件对话框选择exe文件"""
        filepath = filedialog.askopenfilename(
            title=f"选择 {title}",
            filetypes=((f"{title} files", "*.exe"), ("All files", "*.*"))
        )
        if filepath:
            path_var.set(filepath)

    def get_project_path(self, root_directory, project_types, project_paths_map=None):
        """根据项目类型列表和根目录构建项目路径"""
        if not root_directory:
            return []

        # 如果没有提供项目路径映射，从slndir.json加载
        if project_paths_map is None:
            project_paths_map = self.load_project_paths_map()

        # 如果传入的是字符串（向后兼容），转换为列表
        if isinstance(project_types, str):
            project_types = [project_types]

        # 构建完整路径列表
        all_full_paths = []
        for project_type in project_types:
            if project_type in project_paths_map:
                for relative_path in project_paths_map[project_type]:
                    full_path = os.path.join(root_directory, relative_path)
                    all_full_paths.append(full_path)

        return all_full_paths

    def get_project_paths_by_type(self, root_directory, project_types, project_paths_map=None):
        """根据项目类型列表和根目录构建项目路径，按类型分组返回"""
        if not root_directory:
            return {}

        # 如果没有提供项目路径映射，从slndir.json加载
        if project_paths_map is None:
            project_paths_map = self.load_project_paths_map()

        # 如果传入的是字符串（向后兼容），转换为列表
        if isinstance(project_types, str):
            project_types = [project_types]

        # 构建按类型分组的路径字典
        paths_by_type = {}
        for project_type in project_types:
            if project_type in project_paths_map:
                paths_by_type[project_type] = []
                for relative_path in project_paths_map[project_type]:
                    full_path = os.path.join(root_directory, relative_path)
                    paths_by_type[project_type].append(full_path)

        return paths_by_type

    def validate_project_paths(self, root_directory, project_types, project_paths_map=None):
        """验证项目路径是否存在"""
        project_paths = self.get_project_path(root_directory, project_types, project_paths_map)
        if not project_paths:
            return False, "未选择任何项目类型或项目类型无效"

        missing_files = []
        for path in project_paths:
            if not os.path.exists(path):
                missing_files.append(path)

        if missing_files:
            return False, f"以下项目文件不存在:\n" + "\n".join(missing_files)

        return True, "所有项目文件存在"

    def update_project_info_display(self, project_types):
        """更新项目信息显示（在终端中显示项目类型对应的文件列表）"""
        project_info = {
            "editor": [
                "编辑器项目包含以下文件:",
                "• .build-editor\\Engine\\KApplication\\KGameEditor.vcxproj",
                "• .build-editor\\Editor\\ImNodeGraphEditor\\Editor\\ImNodeGraphEditor.vcxproj"
            ],
            "server": [
                "服务器项目包含以下文件:",
                "• Server\\SceneServer\\.build\\SceneServer.sln"
            ],
            "client": [
                "客户端项目包含以下文件:",
                "• .build\\ygame-code.sln"
            ]
        }

        # 如果传入的是字符串（向后兼容），转换为列表
        if isinstance(project_types, str):
            project_types = [project_types]

        info_lines = []
        for project_type in project_types:
            if project_type in project_info:
                info_lines.extend(project_info[project_type])
                info_lines.append("")  # 添加空行分隔

        if info_lines:
            info_text = "\n".join(info_lines) + "-"*40 + "\n"
            self.terminal.config(state='normal')
            self.terminal.insert(tk.END, info_text)
            self.terminal.see(tk.END)
            self.terminal.config(state='disabled')

    def on_project_type_change(self, project_type_vars):
        """处理项目类型多选框变化事件"""
        selected = [ptype for ptype, var in project_type_vars.items() if var.get()]
        if selected:
            self.update_project_info_display(selected)

    def show_current_tab_project_info(self):
        """显示当前标签页的项目信息"""
        if self.ui_widgets:
            current_tab_index = 0  # 默认显示第一个标签页
            if current_tab_index < len(self.ui_widgets):
                current_widgets = self.ui_widgets[current_tab_index]
                selected = [ptype for ptype, var in current_widgets['project_type_vars'].items() if var.get()]
                if selected:
                    self.update_project_info_display(selected)

    def validate_make_bat_files(self, root_directory):
        """验证make.bat文件是否存在"""
        if not root_directory:
            return False, "项目根目录未设置"

        # 需要检查的make.bat文件路径
        make_bat_paths = [
            os.path.join(root_directory, "make.bat"),  # 项目根目录下的make.bat
            os.path.join(root_directory, "Server", "SceneServer", "make.bat")  # Server/SceneServer下的make.bat
        ]

        missing_files = []
        for path in make_bat_paths:
            if not os.path.exists(path):
                missing_files.append(path)

        if missing_files:
            return False, f"以下make.bat文件不存在:\n" + "\n".join(missing_files)

        return True, "所有make.bat文件存在"

    def find_msbuild_path(self):
        """优先查找常见MSBuild路径，找不到再使用vswhere.exe查找"""
        try:
            # 优先查找的MSBuild路径列表（按优先级排序）
            priority_paths = [
                r"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
                r"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
            ]

            # 首先检查优先路径
            for path in priority_paths:
                if os.path.exists(path):
                    return path

            # 如果优先路径都不存在，使用vswhere.exe查找
            # 查找vswhere.exe
            program_files_x86 = os.environ.get('ProgramFiles(x86)', 'C:\\Program Files (x86)')
            vswhere_path = os.path.join(program_files_x86, 'Microsoft Visual Studio', 'Installer', 'vswhere.exe')

            if not os.path.exists(vswhere_path):
                return ""

            # 执行vswhere.exe获取VS安装信息
            process = subprocess.Popen(
                [vswhere_path, '-latest', '-format', 'json'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            output, _ = process.communicate()

            if process.returncode == 0:
                vs_instances = json.loads(output)
                if vs_instances:
                    # 获取最新版本的VS安装路径
                    installation_path = vs_instances[0].get('installationPath', '')
                    if installation_path:
                        # 构建MSBuild路径
                        msbuild_path = os.path.join(
                            installation_path,
                            'MSBuild',
                            'Current',
                            'Bin',
                            'MSBuild.exe'
                        )
                        if os.path.exists(msbuild_path):
                            return msbuild_path

            return ""
        except Exception as e:
            print(f"查找MSBuild路径时出错: {e}")
            return ""

    def generate_log_filenames(self, project_types):
        """根据项目类型和当前日期生成日志文件名"""
        # 获取当前日期
        current_date = datetime.datetime.now().strftime("%Y%m%d")

        # 将项目类型列表转换为字符串
        if isinstance(project_types, list):
            project_name = "_".join(sorted(project_types))
        else:
            project_name = str(project_types)

        # 创建日志目录结构：当前目录/log/日期/
        current_dir = os.path.dirname(os.path.abspath(__file__))
        log_dir = os.path.join(current_dir, "log", current_date)

        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)

        # 生成日志文件名
        build_log = os.path.join(log_dir, f"{project_name}_{current_date}_build.log")
        error_log = os.path.join(log_dir, f"{project_name}_{current_date}_errors.log")

        return build_log, error_log

    def update_log_paths_in_commands(self, custom_commands, build_log, error_log):
        """更新自定义命令中的日志文件路径，支持追加模式"""
        import re

        # 检查是否已存在 /flp:LogFile= 参数
        build_log_pattern = r'/flp:LogFile="[^"]*"[^;\n]*'
        if re.search(build_log_pattern, custom_commands):
            # 如果已存在，替换它
            custom_commands = re.sub(
                build_log_pattern,
                f'/flp:LogFile="{build_log}";Verbosity=detailed',
                custom_commands
            )
        else:
            # 如果不存在，直接添加到末尾
            custom_commands += f'\n/flp:LogFile="{build_log}";Verbosity=detailed'

        # 检查是否已存在 /flp1:LogFile= 参数
        error_log_pattern = r'/flp1:LogFile="[^"]*"[^;\n]*'
        if re.search(error_log_pattern, custom_commands):
            # 如果已存在，替换它
            custom_commands = re.sub(
                error_log_pattern,
                f'/flp1:LogFile="{error_log}";errorsonly',
                custom_commands
            )
        else:
            # 如果不存在，直接添加到末尾
            custom_commands += f'\n/flp1:LogFile="{error_log}";errorsonly'

        return custom_commands

    def load_project_paths_map(self):
        """从slndir.json文件加载项目路径映射"""
        slndir_file = "slndir.json"
        try:
            with open(slndir_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('project_paths_map', {})
        except FileNotFoundError:
            messagebox.showerror("配置文件缺失", f"找不到项目配置文件 {slndir_file}\n程序将退出")
            sys.exit(1)
        except json.JSONDecodeError:
            messagebox.showerror("配置文件错误", f"项目配置文件 {slndir_file} 格式错误\n程序将退出")
            sys.exit(1)
        except Exception as e:
            messagebox.showerror("读取配置文件失败", f"无法读取项目配置文件 {slndir_file}\n错误: {str(e)}\n程序将退出")
            sys.exit(1)

    def check_config_version_compatibility(self, config_version):
        """检查配置文件版本与程序版本的兼容性"""
        if not config_version:
            return "no_version", "配置文件无版本信息，建议更新配置文件格式"

        try:
            # 解析版本号
            current_major = int(self.VERSION.split('.')[0])
            config_major = int(config_version.split('.')[0])

            if current_major != config_major:
                return "incompatible", f"配置文件版本 {config_version} 与程序版本 {self.VERSION} 不兼容\n数据结构可能发生了改变，原来的数据可能会导致编译报错"

            return "compatible", "版本兼容"

        except (ValueError, IndexError):
            return "error", f"无法解析版本号：配置版本 {config_version}，程序版本 {self.VERSION}"

    def create_default_config(self, default_msbuild_path, default_custom_commands):
        """创建默认配置"""
        return [
            {
                "name": "配置 1",
                "is_rebuild": False,
                "is_release": False,
                "project_type": "editor",  # 保持向后兼容性
                "selected_project_types": ["editor"],  # 新的多选字段
                "root_directory": "",
                "sln_path": "",
                "msbuild_path": default_msbuild_path,
                "custom_commands": default_custom_commands,
                "config_version": self.VERSION
            }
        ]

    def load_configs(self):
        """从JSON文件加载配置，如果文件不存在则创建默认配置"""
        # 尝试查找MSBuild路径
        default_msbuild_path = self.find_msbuild_path()

        # 从slndir.json加载项目路径映射
        project_paths_map = self.load_project_paths_map()

        # 默认自定义命令（日志文件路径将在运行时动态生成）
        default_custom_commands = (
            "/nr:false\n"
            "/p:AdditionalOptions=\"/Zm500\"\n"
            "/p:Platform=x64\n"
            "/verbosity:detailed\n"
            "/m\n"
        )

        if not os.path.exists(self.CONFIG_FILE):
            return [
                {
                    "name": "配置 1",
                    "is_rebuild": False,
                    "is_release": False,
                    "project_type": "editor",  # 保持向后兼容性
                    "selected_project_types": ["editor"],  # 新的多选字段
                    "root_directory": "",
                    "sln_path": "",  # 保持向后兼容性
                    "msbuild_path": default_msbuild_path,
                    "custom_commands": default_custom_commands,
                    "config_version": self.VERSION  # 添加版本号
                }
            ]
        try:
            with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

                # 检查是否是新格式（包含版本信息）
                if isinstance(config_data, dict) and 'version' in config_data:
                    # 新格式：{"version": "x.x.x", "configs": [...]}
                    config_version = config_data.get('version')
                    configs = config_data.get('configs', [])
                else:
                    # 旧格式：直接是配置数组，或者单个配置的版本字段
                    if isinstance(config_data, list) and len(config_data) > 0:
                        config_version = config_data[0].get('config_version')
                        configs = config_data
                    else:
                        config_version = None
                        configs = config_data if isinstance(config_data, list) else []

                # 检查版本兼容性
                compatibility_status, message = self.check_config_version_compatibility(config_version)

                if compatibility_status == "no_version":
                    # 无版本信息，提示用户
                    result = messagebox.askyesno(
                        "配置文件版本提示",
                        f"{message}\n\n当前程序版本：{self.VERSION}\n\n是否继续加载并更新配置文件？\n选择'是'将加载配置并添加版本信息\n选择'否'将使用默认配置"
                    )
                    if not result:
                        return self.create_default_config(default_msbuild_path, default_project_paths_map, default_custom_commands)

                elif compatibility_status == "incompatible":
                    # 版本不兼容，警告用户
                    result = messagebox.askyesno(
                        "版本不兼容警告",
                        f"{message}\n\n是否继续加载配置？\n选择'是'将尝试加载并更新配置\n选择'否'将使用默认配置"
                    )
                    if not result:
                        return self.create_default_config(default_msbuild_path, default_custom_commands)

                elif compatibility_status == "error":
                    # 版本解析错误
                    result = messagebox.askyesno(
                        "版本解析错误",
                        f"{message}\n\n是否继续加载配置？\n选择'是'将尝试加载并更新配置\n选择'否'将使用默认配置"
                    )
                    if not result:
                        return self.create_default_config(default_msbuild_path, default_custom_commands)

                # 为旧配置添加新字段的默认值，保持向后兼容性
                for config in configs:
                    if 'project_type' not in config:
                        config['project_type'] = 'editor'
                    if 'root_directory' not in config:
                        config['root_directory'] = ''
                    if 'selected_project_types' not in config:
                        # 如果没有新的多选字段，从旧的单选字段创建
                        config['selected_project_types'] = [config.get('project_type', 'editor')]
                    # 移除旧的project_paths_map字段（现在从slndir.json读取）
                    if 'project_paths_map' in config:
                        del config['project_paths_map']
                    # 更新配置版本号
                    config['config_version'] = self.VERSION

                return configs
        except (json.JSONDecodeError, FileNotFoundError):
            messagebox.showerror("错误", "无法加载配置文件，将使用默认配置。")
            return self.create_default_config(default_msbuild_path, default_custom_commands)

    def save_configs(self):
        """从UI控件读取当前状态并保存到JSON文件"""
        for i, tab_widgets in enumerate(self.ui_widgets):
            self.configs[i]['name'] = self.notebook.tab(i, "text")
            self.configs[i]['is_rebuild'] = tab_widgets['build_mode_var'].get() == "Rebuild"
            self.configs[i]['is_release'] = tab_widgets['config_mode_var'].get() == "Release"

            # 保存多选的项目类型
            selected_types = [ptype for ptype, var in tab_widgets['project_type_vars'].items() if var.get()]
            self.configs[i]['selected_project_types'] = selected_types
            # 保持向后兼容性，保存第一个选中的项目类型到旧字段
            self.configs[i]['project_type'] = selected_types[0] if selected_types else "editor"

            self.configs[i]['root_directory'] = tab_widgets['root_dir_var'].get()
            self.configs[i]['msbuild_path'] = tab_widgets['msbuild_path_var'].get()
            self.configs[i]['custom_commands'] = tab_widgets['custom_cmd_text'].get("1.0", tk.END).strip()

            # 保持向后兼容性，保留sln_path字段但设为空
            self.configs[i]['sln_path'] = ""

            # 更新配置版本号
            self.configs[i]['config_version'] = self.VERSION

        try:
            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, indent=4, ensure_ascii=False)
        except IOError:
            messagebox.showerror("错误", "无法保存配置文件。")

    def is_process_running(self):
        """检查是否有构建进程正在运行"""
        return (hasattr(self, 'build_process') and
                self.build_process and
                self.build_process.poll() is None)

    def start_build_thread(self):
        """启动编译线程，防止UI冻结"""
        # 检查是否已有进程在运行
        if self.is_process_running():
            messagebox.showwarning("警告", "已有构建进程正在运行，请等待完成或先中断当前进程！")
            return

        current_tab_index = self.notebook.index(self.notebook.select())
        current_widgets = self.ui_widgets[current_tab_index]

        # 验证项目根目录
        root_directory = current_widgets['root_dir_var'].get()
        if not root_directory:
            messagebox.showerror("错误", "请设置项目根目录！")
            return

        if not os.path.exists(root_directory):
            messagebox.showerror("错误", "项目根目录不存在！")
            return

        # 验证项目类型和项目文件
        selected_types = [ptype for ptype, var in current_widgets['project_type_vars'].items() if var.get()]
        if not selected_types:
            messagebox.showerror("错误", "请至少选择一个项目类型！")
            return

        # 验证项目路径（从slndir.json读取）
        is_valid, message = self.validate_project_paths(root_directory, selected_types)
        if not is_valid:
            messagebox.showerror("错误", f"项目文件验证失败:\n{message}")
            return

        # 验证MSBuild路径
        msbuild_path = current_widgets['msbuild_path_var'].get()
        if not msbuild_path or not os.path.exists(msbuild_path):
            messagebox.showerror("错误", "MSBuild.exe 文件路径无效或不存在！")
            return

        # 禁用UI
        self.toggle_ui_lock(lock=True)

        # 清空终端
        self.terminal.config(state='normal')
        self.terminal.delete('1.0', tk.END)
        self.terminal.config(state='disabled')

        build_thread = threading.Thread(target=self._build_worker, args=(current_widgets,), daemon=True)
        build_thread.start()

    def start_root_make_thread(self):
        """启动根目录make.bat执行线程，防止UI冻结"""
        # 检查是否已有进程在运行
        if self.is_process_running():
            messagebox.showwarning("警告", "已有构建进程正在运行，请等待完成或先中断当前进程！")
            return

        current_tab_index = self.notebook.index(self.notebook.select())
        current_widgets = self.ui_widgets[current_tab_index]

        # 验证项目根目录
        root_directory = current_widgets['root_dir_var'].get()
        if not root_directory:
            messagebox.showerror("错误", "请设置项目根目录！")
            return

        if not os.path.exists(root_directory):
            messagebox.showerror("错误", "项目根目录不存在！")
            return

        # 验证根目录make.bat文件是否存在
        root_make_bat = os.path.join(root_directory, "make.bat")
        if not os.path.exists(root_make_bat):
            messagebox.showerror("错误", f"根目录make.bat文件不存在:\n{root_make_bat}")
            return

        # 禁用UI
        self.toggle_ui_lock(lock=True)

        # 清空终端
        self.terminal.config(state='normal')
        self.terminal.delete('1.0', tk.END)
        self.terminal.config(state='disabled')

        make_thread = threading.Thread(target=self._root_make_worker, args=(root_directory,), daemon=True)
        make_thread.start()

    def start_server_make_thread(self):
        """启动服务器make.bat执行线程，防止UI冻结"""
        # 检查是否已有进程在运行
        if self.is_process_running():
            messagebox.showwarning("警告", "已有构建进程正在运行，请等待完成或先中断当前进程！")
            return

        current_tab_index = self.notebook.index(self.notebook.select())
        current_widgets = self.ui_widgets[current_tab_index]

        # 验证项目根目录
        root_directory = current_widgets['root_dir_var'].get()
        if not root_directory:
            messagebox.showerror("错误", "请设置项目根目录！")
            return

        if not os.path.exists(root_directory):
            messagebox.showerror("错误", "项目根目录不存在！")
            return

        # 验证服务器make.bat文件是否存在
        server_make_bat = os.path.join(root_directory, "Server", "SceneServer", "make.bat")
        if not os.path.exists(server_make_bat):
            messagebox.showerror("错误", f"服务器make.bat文件不存在:\n{server_make_bat}")
            return

        # 禁用UI
        self.toggle_ui_lock(lock=True)

        # 清空终端
        self.terminal.config(state='normal')
        self.terminal.delete('1.0', tk.END)
        self.terminal.config(state='disabled')

        make_thread = threading.Thread(target=self._server_make_worker, args=(root_directory,), daemon=True)
        make_thread.start()

    def stop_build_process(self):
        """停止当前正在执行的编译进程"""
        if hasattr(self, 'build_process') and self.build_process:
            # 检查进程是否仍在运行
            if self.is_process_running():
                try:
                    # 终止进程
                    self.build_process.terminate()
                    # 等待一小段时间让进程正常终止
                    try:
                        self.build_process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        # 如果进程没有在2秒内终止，强制杀死
                        self.build_process.kill()
                        self.build_process.wait()

                    self.build_queue.put(f"\n" + "="*50 + f"\n用户中断了编译过程\n" + "="*50 + "\n")

                    # 清理资源
                    self._cleanup_build_process()

                except Exception as e:
                    self.build_queue.put(f"\n停止进程时发生错误: {str(e)}\n")
                    # 强制清理
                    self._cleanup_build_process()
            else:
                messagebox.showinfo("提示", "当前进程已经结束。")
        else:
            messagebox.showinfo("提示", "当前没有正在执行的编译进程。")

    def _build_worker(self, widgets):
        """在后台线程中执行MSBuild命令"""
        self.output_threads = []  # 存储输出线程的引用
        try:
            # 1. 获取项目信息
            root_directory = widgets['root_dir_var'].get()
            selected_types = [ptype for ptype, var in widgets['project_type_vars'].items() if var.get()]
            
            # 按项目类型分组获取项目路径（从slndir.json读取）
            paths_by_type = self.get_project_paths_by_type(root_directory, selected_types)

            if not paths_by_type:
                self.build_queue.put("错误: 无法获取项目路径\n")
                return

            # 2. 构建基础命令参数
            msbuild_path = widgets['msbuild_path_var'].get()
            build_mode = widgets['build_mode_var'].get()
            config_mode = widgets['config_mode_var'].get()

            # 获取基础自定义命令（不包含日志参数）
            base_custom_commands = widgets['custom_cmd_text'].get("1.0", tk.END).strip()

            # 3. 按项目类型分别编译
            total_projects = sum(len(paths) for paths in paths_by_type.values())
            current_project = 0

            for project_type, project_paths in paths_by_type.items():
                # 为当前项目类型生成日志文件名
                build_log, error_log = self.generate_log_filenames([project_type])

                # 为当前项目类型更新日志路径
                custom_commands = self.update_log_paths_in_commands(base_custom_commands, build_log, error_log)

                custom_args = []
                if custom_commands:
                    for line in custom_commands.strip().split('\n'):
                        if line.strip():
                            custom_args.append(line.strip())

                # 编译当前项目类型的所有项目
                for project_path in project_paths:
                    current_project += 1
                    self.build_queue.put(f"\n{'='*50}\n")
                    self.build_queue.put(f"编译项目 {current_project}/{total_projects} ({project_type}): {os.path.basename(project_path)}\n")
                    self.build_queue.put(f"项目路径: {project_path}\n")
                    self.build_queue.put(f"日志文件: {build_log}\n")
                    self.build_queue.put(f"{'='*50}\n")

                    # 构建单个项目的命令
                    cmd = [f'"{msbuild_path}"', f'"{project_path}"']
                    cmd.append(f'/t:{build_mode}')
                    cmd.append(f'/p:Configuration={config_mode}')
                    cmd.extend(custom_args)

                    full_command = " ".join(cmd)
                    self.build_queue.put(f"执行命令: {full_command}\n" + "-"*30 + "\n")

                    # 4. 执行单个项目的编译命令
                    # 设置环境变量，强制使用英文输出
                    env = os.environ.copy()  # 复制当前环境变量
                    env['VSLANG'] = '1033'  # Visual Studio 输出语言为英文
                    env['DOTNET_CLI_UI_LANGUAGE'] = 'en'  # .NET CLI 输出语言为英文

                    self.build_process = subprocess.Popen(
                        full_command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        encoding='utf-8',
                        errors='replace',
                        shell=True,
                        bufsize=1,  # 行缓冲
                        env=env,  # 使用修改后的环境变量
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
                    )

                    # 5. 创建输出读取线程
                    def read_output_stream(pipe, queue, prefix=""):
                        try:
                            with pipe:  # 使用上下文管理器自动关闭pipe
                                for line in iter(pipe.readline, ''):
                                    if not getattr(self, 'build_process', None):  # 检查进程是否已被终止
                                        break
                                    queue.put(prefix + line)
                        except Exception as e:
                            queue.put(f"\n{prefix}读取输出时发生错误: {str(e)}\n")

                    # 启动输出读取线程
                    stdout_thread = threading.Thread(
                        target=read_output_stream,
                        args=(self.build_process.stdout, self.build_queue, ""),
                        daemon=True
                    )
                    stderr_thread = threading.Thread(
                        target=read_output_stream,
                        args=(self.build_process.stderr, self.build_queue, "[ERROR] "),
                        daemon=True
                    )

                    self.output_threads = [stdout_thread, stderr_thread]
                    stdout_thread.start()
                    stderr_thread.start()

                    # 6. 等待当前项目编译完成
                    return_code = self.build_process.wait()

                    # 等待输出线程完成
                    for thread in self.output_threads:
                        thread.join(timeout=1)

                    self.build_queue.put(f"\n" + "-"*30 + f"\n项目 {os.path.basename(project_path)} 编译结束，返回代码: {return_code}\n")

                    # 如果编译失败且不是最后一个项目，询问是否继续
                    if return_code != 0 and current_project < total_projects:
                        self.build_queue.put(f"警告: 项目编译失败，但将继续编译下一个项目...\n")

            # 7. 所有项目编译完成
            self.build_queue.put(f"\n" + "="*50 + f"\n所有项目编译完成！\n" + "="*50 + "\n")

        except FileNotFoundError:
            self.build_queue.put(f"\n错误: MSBuild.exe 未找到。\n请确保路径正确。")
        except Exception as e:
            self.build_queue.put(f"\n发生未知错误: {e}")
        finally:
            self._cleanup_build_process()

    def _root_make_worker(self, root_directory):
        """在后台线程中执行根目录make.bat命令"""
        self.output_threads = []  # 存储输出线程的引用
        try:
            # 根目录make.bat文件信息
            bat_info = {
                "path": os.path.join(root_directory, "make.bat"),
                "name": "项目根目录make.bat",
                "working_dir": root_directory
            }

            self.build_queue.put(f"\n{'='*50}\n")
            self.build_queue.put(f"执行: {bat_info['name']}\n")
            self.build_queue.put(f"文件路径: {bat_info['path']}\n")
            self.build_queue.put(f"工作目录: {bat_info['working_dir']}\n")
            self.build_queue.put(f"{'='*50}\n")

            # 构建命令
            command = f'"{bat_info["path"]}"'
            self.build_queue.put(f"执行命令: {command}\n" + "-"*30 + "\n")

            # 设置环境变量
            env = os.environ.copy()
            env['VSLANG'] = '1033'  # Visual Studio 输出语言为英文
            env['DOTNET_CLI_UI_LANGUAGE'] = 'en'  # .NET CLI 输出语言为英文

            # 执行命令
            self.build_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                shell=True,
                bufsize=1,  # 行缓冲
                env=env,
                cwd=bat_info['working_dir'],  # 设置工作目录
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
            )

            # 创建输出读取线程
            def read_output_stream(pipe, queue, prefix=""):
                try:
                    with pipe:
                        for line in iter(pipe.readline, ''):
                            if not getattr(self, 'build_process', None):
                                break
                            queue.put(prefix + line)
                except Exception as e:
                    queue.put(f"\n{prefix}读取输出时发生错误: {str(e)}\n")

            # 启动输出读取线程
            stdout_thread = threading.Thread(
                target=read_output_stream,
                args=(self.build_process.stdout, self.build_queue, ""),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=read_output_stream,
                args=(self.build_process.stderr, self.build_queue, "[ERROR] "),
                daemon=True
            )

            self.output_threads = [stdout_thread, stderr_thread]
            stdout_thread.start()
            stderr_thread.start()

            # 等待make.bat执行完成
            return_code = self.build_process.wait()

            # 等待输出线程完成
            for thread in self.output_threads:
                thread.join(timeout=1)

            self.build_queue.put(f"\n" + "-"*30 + f"\n{bat_info['name']} 执行结束，返回代码: {return_code}\n")
            self.build_queue.put(f"\n" + "="*50 + f"\n根目录make.bat执行完成！\n" + "="*50 + "\n")

        except FileNotFoundError:
            self.build_queue.put(f"\n错误: make.bat 文件未找到。\n请确保路径正确。")
        except Exception as e:
            self.build_queue.put(f"\n发生未知错误: {e}")
        finally:
            self._cleanup_build_process()

    def _server_make_worker(self, root_directory):
        """在后台线程中执行服务器make.bat命令"""
        self.output_threads = []  # 存储输出线程的引用
        try:
            # 服务器make.bat文件信息
            bat_info = {
                "path": os.path.join(root_directory, "Server", "SceneServer", "make.bat"),
                "name": "SceneServer make.bat",
                "working_dir": os.path.join(root_directory, "Server", "SceneServer")
            }

            self.build_queue.put(f"\n{'='*50}\n")
            self.build_queue.put(f"执行: {bat_info['name']}\n")
            self.build_queue.put(f"文件路径: {bat_info['path']}\n")
            self.build_queue.put(f"工作目录: {bat_info['working_dir']}\n")
            self.build_queue.put(f"{'='*50}\n")

            # 构建命令
            command = f'"{bat_info["path"]}"'
            self.build_queue.put(f"执行命令: {command}\n" + "-"*30 + "\n")

            # 设置环境变量
            env = os.environ.copy()
            env['VSLANG'] = '1033'  # Visual Studio 输出语言为英文
            env['DOTNET_CLI_UI_LANGUAGE'] = 'en'  # .NET CLI 输出语言为英文

            # 执行命令
            self.build_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                shell=True,
                bufsize=1,  # 行缓冲
                env=env,
                cwd=bat_info['working_dir'],  # 设置工作目录
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
            )

            # 创建输出读取线程
            def read_output_stream(pipe, queue, prefix=""):
                try:
                    with pipe:
                        for line in iter(pipe.readline, ''):
                            if not getattr(self, 'build_process', None):
                                break
                            queue.put(prefix + line)
                except Exception as e:
                    queue.put(f"\n{prefix}读取输出时发生错误: {str(e)}\n")

            # 启动输出读取线程
            stdout_thread = threading.Thread(
                target=read_output_stream,
                args=(self.build_process.stdout, self.build_queue, ""),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=read_output_stream,
                args=(self.build_process.stderr, self.build_queue, "[ERROR] "),
                daemon=True
            )

            self.output_threads = [stdout_thread, stderr_thread]
            stdout_thread.start()
            stderr_thread.start()

            # 等待make.bat执行完成
            return_code = self.build_process.wait()

            # 等待输出线程完成
            for thread in self.output_threads:
                thread.join(timeout=1)

            self.build_queue.put(f"\n" + "-"*30 + f"\n{bat_info['name']} 执行结束，返回代码: {return_code}\n")
            self.build_queue.put(f"\n" + "="*50 + f"\n服务器make.bat执行完成！\n" + "="*50 + "\n")

        except FileNotFoundError:
            self.build_queue.put(f"\n错误: make.bat 文件未找到。\n请确保路径正确。")
        except Exception as e:
            self.build_queue.put(f"\n发生未知错误: {e}")
        finally:
            self._cleanup_build_process()

    def process_build_queue(self):
        """处理来自编译线程的消息队列，更新UI"""
        try:
            # 批量处理队列中的消息，避免频繁更新UI
            messages = []
            while True:
                try:
                    msg = self.build_queue.get_nowait()
                    if msg is None:  # 完成信号
                        # 先处理剩余消息
                        if messages:
                            self.terminal.config(state='normal')
                            for message in messages:
                                self.terminal.insert(tk.END, message)
                            self.terminal.see(tk.END)
                            self.terminal.config(state='disabled')
                        # 然后解锁UI并显示完成消息
                        self.toggle_ui_lock(lock=False)
                        # 检查是否是用户中断，如果是则不显示完成消息
                        if messages and not any("用户中断了编译过程" in msg for msg in messages):
                            messagebox.showinfo("完成", "编译过程已结束！")
                        return
                    else:
                        messages.append(msg)
                except queue.Empty:
                    break
            
            # 批量更新终端显示
            if messages:
                self.terminal.config(state='normal')
                for message in messages:
                    self.terminal.insert(tk.END, message)
                self.terminal.see(tk.END)  # 自动滚动到底部
                self.terminal.config(state='disabled')
                self.update_idletasks()  # 强制更新UI
                
        except Exception as e:
            print(f"处理队列时出错: {e}")
        finally:
            # 更高频率的检查，提高流式输出的实时性
            self.after(50, self.process_build_queue)

    def toggle_ui_lock(self, lock: bool):
        """锁定或解锁UI控件"""
        state = 'disabled' if lock else 'normal'
        stop_state = 'normal' if lock else 'disabled'  # 中断按钮状态相反

        # 锁定所有配置页的控件和按钮
        for tab_widgets in self.ui_widgets:
            # 锁定按钮
            if 'compile_button' in tab_widgets:
                tab_widgets['compile_button'].config(state=state)
            if 'stop_button' in tab_widgets:
                tab_widgets['stop_button'].config(state=stop_state)  # 编译时启用中断按钮
            if 'root_make_button' in tab_widgets:
                tab_widgets['root_make_button'].config(state=state)
            if 'server_make_button' in tab_widgets:
                tab_widgets['server_make_button'].config(state=state)
            if 'log_button' in tab_widgets:
                tab_widgets['log_button'].config(state=state)

            # 锁定其他控件
            for control in tab_widgets['controls']:
                try:
                    control.config(state=state)
                except tk.TclError: # Text widget has a different state handling
                    control.config(state='disabled' if lock else 'normal')

    def _cleanup_build_process(self):
        """清理编译进程和相关资源"""
        # 清理进程
        if hasattr(self, 'build_process') and self.build_process:
            try:
                self.build_process.terminate()
                self.build_process.kill()  # 强制结束进程
                self.build_process = None
            except:
                pass

        # 清理输出线程
        if hasattr(self, 'output_threads'):
            for thread in self.output_threads:
                try:
                    thread.join(timeout=0.5)
                except:
                    pass
            self.output_threads = []

        # 发送完成信号
        self.build_queue.put(None)

    def on_closing(self):
        """关闭窗口时的处理程序"""
        # if messagebox.askokcancel("退出", "您确定要退出吗？配置将自动保存。"):
        #     # 清理所有资源
        self._cleanup_build_process()
        self.save_configs()
        self.destroy()

    def setup_notebook_menu(self):
        """为Notebook标签页设置右键菜单和双击事件"""
        self.notebook_menu = tk.Menu(self, tearoff=0)
        self.notebook_menu.add_command(label="新增配置", command=self.add_tab)
        self.notebook_menu.add_command(label="重命名此配置", command=self.rename_tab)
        self.notebook_menu.add_separator()
        self.notebook_menu.add_command(label="删除此配置", command=self.delete_tab)
        self.notebook_menu.add_separator()
        self.notebook_menu.add_command(label="关于", command=self.show_about)

        self.notebook.bind("<Button-3>", self.show_notebook_menu)
        self.notebook.bind("<Double-Button-1>", self.on_tab_double_click)

    def on_tab_double_click(self, event):
        """处理标签页的双击事件"""
        try:
            # 获取点击的标签页
            clicked_tab = self.notebook.tk.call(self.notebook._w, "identify", "tab", event.x, event.y)
            if clicked_tab != "":
                self.notebook.select(clicked_tab)  # 选中被点击的标签
                self.rename_tab()  # 调用重命名函数
        except Exception:
            pass  # 如果点击位置不是标签，则忽略

    def show_notebook_menu(self, event):
        """在右键点击处显示菜单"""
        try:
            # 识别点击的标签页
            element = self.notebook.identify(event.x, event.y)
            if element == "label":
                # 获取具体的标签页索引
                tab_index = self.notebook.tk.call(self.notebook._w, "identify", "tab", event.x, event.y)
                if tab_index != "":
                    self.notebook.select(tab_index) # 选中点击的标签
            self.notebook_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.notebook_menu.grab_release()

    def add_tab(self):
        """新增一个配置标签页"""
        new_config_name = f"新配置 {len(self.configs) + 1}"

        # 默认自定义命令（日志文件路径将在运行时动态生成）
        default_custom_commands = (
            "/nr:false\n"
            "/p:AdditionalOptions=\"/Zm500\"\n"
            "/p:Platform=x64\n"
            "/verbosity:detailed\n"
            "/m\n"
            "/fl\n"
            "/fl1\n"
        )

        new_config = {
            "name": new_config_name,
            "is_rebuild": False,
            "is_release": False,
            "project_type": "editor",  # 保持向后兼容性
            "selected_project_types": ["editor"],  # 新的多选字段
            "root_directory": "",
            "sln_path": "",  # 保持向后兼容性
            "msbuild_path": self.find_msbuild_path(),
            "custom_commands": default_custom_commands,
            "config_version": self.VERSION  # 添加版本号
        }
        self.configs.append(new_config)

        tab = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(tab, text=new_config_name)

        tab_widgets = self.create_tab_content(tab, new_config)
        self.ui_widgets.append(tab_widgets)

        self.notebook.select(tab) # 自动切换到新标签页

    def rename_tab(self):
        """重命名当前的标签页"""
        from tkinter.simpledialog import askstring
        current_tab_index = self.notebook.index(self.notebook.select())
        current_name = self.notebook.tab(current_tab_index, "text")
        
        new_name = askstring("重命名", "输入新的配置名称:", initialvalue=current_name)
        if new_name:
            self.notebook.tab(current_tab_index, text=new_name)
            self.configs[current_tab_index]['name'] = new_name

    def delete_tab(self):
        """删除当前的标签页"""
        if len(self.configs) <= 1:
            messagebox.showwarning("警告", "无法删除最后一个配置。")
            return
            
        current_tab_index = self.notebook.index(self.notebook.select())
        tab_name = self.notebook.tab(current_tab_index, "text")

        if messagebox.askyesno("确认删除", f"您确定要删除配置 '{tab_name}' 吗？"):
            self.notebook.forget(current_tab_index)
            self.configs.pop(current_tab_index)
            self.ui_widgets.pop(current_tab_index)

    def show_about(self):
        """显示版本信息"""
        about_text = f"""YGame 编译工具

版本: {self.VERSION_INFO['version']}
发布日期: {self.VERSION_INFO['date']}
作者: {self.VERSION_INFO['author']}

更新内容:
"""
        for change in self.VERSION_INFO['changes']:
            about_text += f"{change}\n"

        messagebox.showinfo("关于", about_text)

def create_backup_and_restart():
    """创建当前文件的备份并重启备份版本"""
    try:
        # 获取当前文件路径
        if getattr(sys, 'frozen', False):
            current_file = sys.executable
            is_exe = True
        else:
            current_file = os.path.abspath(__file__)
            is_exe = False

        # 生成备份文件名
        file_dir = os.path.dirname(current_file)
        file_name = os.path.basename(current_file)
        name_without_ext = os.path.splitext(file_name)[0]
        file_ext = os.path.splitext(file_name)[1]
        backup_file = os.path.join(file_dir, f"{name_without_ext}_bak{file_ext}")

        # 复制当前文件到备份文件
        shutil.copy2(current_file, backup_file)
        print(f"已创建备份文件: {backup_file}")

        # 启动备份版本并传递--nobak参数
        if is_exe:
            subprocess.Popen([backup_file, "--nobak"])
        else:
            subprocess.Popen([sys.executable, backup_file, "--nobak"])
        return True

    except Exception as e:
        print(f"创建备份时发生错误: {e}")
        return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YGame 编译工具')
    parser.add_argument('--nobak', action='store_true',
                       help='跳过备份创建，直接运行程序')
    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()

    # 获取当前文件名
    current_filename = os.path.basename(__file__ if not getattr(sys, 'frozen', False) else sys.executable)

    # 检查是否需要创建备份：没有--nobak参数 且 文件名不包含"bak"
    should_backup = not args.nobak and "bak" not in current_filename

    if should_backup:
        if create_backup_and_restart():
            # 如果成功创建备份并重启，退出当前进程
            sys.exit(0)
    

    # 正常启动程序
    app = MSBuildTool()
    app.mainloop()
