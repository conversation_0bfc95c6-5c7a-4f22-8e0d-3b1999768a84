﻿Build started 15/8/2025 上午 10:15:27.
Logging verbosity is set to: Detailed.     0>Process = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
       MSBuild executable path = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
       Command line arguments = ""C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"  "D:/ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj" /t:Build /p:Configuration=Debug /nr:false /p:AdditionalOptions="/Zm500" /p:Platform=x64 /verbosity:detailed /m /flp:LogFile="g:\project\msbuildtool\log\20250815\editor_20250815_build.log";Verbosity=detailed /flp1:LogFile="g:\project\msbuildtool\log\20250815\editor_20250815_errors.log";errorsonly"
       Current directory = "G:\project\msbuildtool"
       MSBuild version = "17.14.18+a338add32"
       Based on the Windows registry key LongPathsEnabled, the LongPaths feature is disabled.
       Some command line switches were read from the auto-response file "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.rsp". To disable this file, use the "-noAutoResponse" switch.
       
       Property reassignment: $(MSBuildExtensionsPath)="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild" (previous value: "C:\Program Files (x86)\MSBuild") at 
       Property reassignment: $(MSBuildExtensionsPath32)="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild" (previous value: "C:\Program Files (x86)\MSBuild") at 
       The "Platform" property is a global property, and cannot be modified.
     1>Project "D:\ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj" on node 1 (Build target(s)).
     1>D:\ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj(24,3): error MSB4019: The imported project "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.Cpp.Default.props" was not found. Confirm that the expression in the Import declaration "$(VCTargetsPath)\Microsoft.Cpp.Default.props", which evaluated to "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\\Microsoft.Cpp.Default.props", is correct, and that the file exists on disk.
     1>Done Building Project "D:\ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj" (Build target(s)) -- FAILED.

Build FAILED.

       "D:\ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj" (Build target) (1) ->
         D:\ygame_code\.build-editor\Editor\ImNodeGraphEditor\Editor\ImNodeGraphEditor.vcxproj(24,3): error MSB4019: The imported project "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.Cpp.Default.props" was not found. Confirm that the expression in the Import declaration "$(VCTargetsPath)\Microsoft.Cpp.Default.props", which evaluated to "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\\Microsoft.Cpp.Default.props", is correct, and that the file exists on disk.

    0 Warning(s)
    1 Error(s)

Time Elapsed 00:00:00.01
