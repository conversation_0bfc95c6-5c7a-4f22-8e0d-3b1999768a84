# 🚀 YGameBuildTool 快速使用指南

## 📥 下载和启动

1. 下载 `YGameBuildTool_v1.8.0.exe`
2. 双击运行（无需安装）

## ⚡ 5分钟快速配置

### 第一步：设置项目路径
```
项目根目录: [浏览] → 选择你的游戏项目根目录
```

### 第二步：选择项目类型
```
☑️ editor  - 编辑器项目
☑️ server  - 服务器项目  
☑️ client  - 客户端项目
```

### 第三步：选择编译模式
```
编译模式: Build ▼     (Build=增量编译, Rebuild=完全重建)
配置模式: Release ▼   (Debug=调试版, Release=发布版)
```

### 第四步：开始编译
```
点击 [编译] 按钮
```

## 🎯 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 配置1 │ 配置2 │ 配置3 │ [+新增]                              │
├─────────────────────────────────────────────────────────────┤
│ 左侧控制面板              │ 右侧自定义命令                      │
│ ┌─────────────────────┐   │ ┌─────────────────────────────────┐ │
│ │ 编译模式: Build ▼   │   │ │ /nr:false                       │ │
│ │ 配置模式: Release ▼ │   │ │ /p:Platform=x64                 │ │
│ │ 项目类型:           │   │ │ /verbosity:detailed             │ │
│ │ ☑️ editor           │   │ │ /m                              │ │
│ │ ☑️ server           │   │ │ /fl                             │ │
│ │ ☑️ client           │   │ │ ...                             │ │
│ │ 项目根目录: [浏览]  │   │ │                                 │ │
│ │ MSBuild路径: [浏览] │   │ │                                 │ │
│ │                     │   │ │                                 │ │
│ │ [编译] [中断]       │   │ │                                 │ │
│ │ [make.bat] [日志]   │   │ │                                 │ │
│ └─────────────────────┘   │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 编译输出终端                                                  │
│ ================================================================ │
│ 编译项目 1/2: KGameEditor.vcxproj                             │
│ 执行命令: "MSBuild.exe" "项目路径" /t:Build /p:Configuration=Release │
│ ---------------------------------------------------------------- │
│ Microsoft (R) Build Engine version 17.0.0                     │
│ Building...                                                    │
│ ================================================================ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 常用操作

### 配置管理
| 操作 | 方法 |
|------|------|
| 新增配置 | 右键标签页 → "新增配置" |
| 重命名配置 | 双击标签页标题 |
| 删除配置 | 右键标签页 → "删除此配置" |
| 切换配置 | 点击标签页 |

### 编译操作
| 按钮 | 功能 |
|------|------|
| 编译 | 开始编译选中的项目类型 |
| 中断 | 停止当前编译过程 |
| 执行make.bat | 运行项目根目录的make.bat |
| 执行server/make.bat | 运行服务器目录的make.bat |
| 日志位置 | 打开日志文件夹 |

## ⚠️ 注意事项

### ✅ 正确设置
- 确保项目根目录包含相应的项目文件
- MSBuild路径通常自动检测，无需手动设置
- 至少选择一个项目类型才能编译

### ❌ 常见错误
- 项目根目录设置错误 → 检查路径是否正确
- 没有选择项目类型 → 至少勾选一个项目类型
- MSBuild路径无效 → 确保安装了Visual Studio

### 🔄 进程管理
- 同时只能运行一个编译任务
- 如需切换任务，先点击"中断"停止当前任务
- 编译时所有按钮会被禁用，只有"中断"按钮可用

## 📁 项目文件结构

工具会自动查找以下项目文件：

```
项目根目录/
├── .build-editor/Engine/KApplication/KGameEditor.vcxproj          (editor)
├── .build-editor/Editor/ImNodeGraphEditor/Editor/ImNodeGraphEditor.vcxproj  (editor)
├── Server/SceneServer/.build/SceneServer.sln                      (server)
├── .build/ygame-code.sln                                          (client)
├── make.bat                                                       (根目录脚本)
└── Server/make.bat                                                (服务器脚本)
```

## 🎨 自定义编译参数

在右侧文本框中可以添加MSBuild参数：

```bash
# 常用参数示例
/nr:false                           # 禁用节点重用
/p:Platform=x64                     # 指定64位平台
/verbosity:detailed                 # 详细输出
/m                                  # 多核并行编译
/fl                                 # 输出到日志文件
/p:AdditionalOptions="/Zm500"       # 增加编译器内存限制
/p:Configuration=Release            # 指定配置（会被界面设置覆盖）
```

## 🆘 快速故障排除

| 问题 | 解决方案 |
|------|----------|
| 提示"项目根目录不存在" | 重新选择正确的项目根目录 |
| 提示"MSBuild.exe无效" | 确保安装Visual Studio 2019/2022 |
| 提示"项目文件验证失败" | 检查项目文件是否存在于预期位置 |
| 编译输出乱码 | 工具已自动设置英文输出，重启程序 |
| 界面显示模糊 | 工具已启用高DPI支持，检查系统设置 |

## 💡 使用技巧

1. **多配置管理**: 为不同的编译需求创建专门的配置（如Debug配置、Release配置、特定项目配置等）

2. **快速切换**: 使用标签页快速在不同配置间切换，每个配置的设置独立保存

3. **实时监控**: 编译过程中可以实时查看输出，及时发现问题

4. **日志查看**: 编译完成后可以通过"日志位置"按钮查看详细的编译日志

5. **进程管理**: 如果编译卡住，使用"中断"按钮安全停止，然后重新开始

---

🎉 **恭喜！你已经掌握了 YGameBuildTool 的基本使用方法！**

如有问题，请查看完整的 [README.md](README.md) 文档或提交 Issue。
